// Test file to demonstrate the new PostTranslateModifyHook functionality with listeners and routes
package main

import (
	"context"
	"fmt"
	"log"

	listenerv3 "github.com/envoyproxy/go-control-plane/envoy/config/listener/v3"
	routev3 "github.com/envoyproxy/go-control-plane/envoy/config/route/v3"
	clusterv3 "github.com/envoyproxy/go-control-plane/envoy/config/cluster/v3"
	tlsv3 "github.com/envoyproxy/go-control-plane/envoy/extensions/transport_sockets/tls/v3"

	pb "github.com/envoyproxy/gateway/proto/extension"
)

// ExampleExtensionServer demonstrates the new PostTranslateModify functionality
type ExampleExtensionServer struct {
	pb.UnimplementedEnvoyGatewayExtensionServer
}

// PostTranslateModify demonstrates how extensions can now modify listeners and routes
// in addition to clusters and secrets
func (s *ExampleExtensionServer) PostTranslateModify(ctx context.Context, req *pb.PostTranslateModifyRequest) (*pb.PostTranslateModifyResponse, error) {
	fmt.Printf("Received PostTranslateModify request with:\n")
	fmt.Printf("- %d clusters\n", len(req.Clusters))
	fmt.Printf("- %d secrets\n", len(req.Secrets))
	fmt.Printf("- %d listeners\n", len(req.Listeners))
	fmt.Printf("- %d routes\n", len(req.Routes))

	// Clone all existing resources
	response := &pb.PostTranslateModifyResponse{
		Clusters:  make([]*clusterv3.Cluster, len(req.Clusters)),
		Secrets:   make([]*tlsv3.Secret, len(req.Secrets)),
		Listeners: make([]*listenerv3.Listener, len(req.Listeners)),
		Routes:    make([]*routev3.RouteConfiguration, len(req.Routes)),
	}

	// Copy clusters
	for i, cluster := range req.Clusters {
		response.Clusters[i] = cluster
	}

	// Copy secrets
	for i, secret := range req.Secrets {
		response.Secrets[i] = secret
	}

	// Copy and potentially modify listeners
	for i, listener := range req.Listeners {
		response.Listeners[i] = listener
		// Example: Add a comment to demonstrate listener modification capability
		if response.Listeners[i].Metadata == nil {
			response.Listeners[i].Metadata = &listenerv3.Listener_Metadata{}
		}
		fmt.Printf("Processing listener: %s\n", listener.Name)
	}

	// Copy and potentially modify routes
	for i, route := range req.Routes {
		response.Routes[i] = route
		// Example: Add a comment to demonstrate route modification capability
		fmt.Printf("Processing route: %s\n", route.Name)
	}

	// Example: Add a new cluster (existing functionality)
	response.Clusters = append(response.Clusters, &clusterv3.Cluster{
		Name: "extension-injected-cluster",
	})

	// Example: Add a new listener (new functionality)
	response.Listeners = append(response.Listeners, &listenerv3.Listener{
		Name: "extension-injected-listener",
	})

	// Example: Add a new route (new functionality)
	response.Routes = append(response.Routes, &routev3.RouteConfiguration{
		Name: "extension-injected-route",
	})

	fmt.Printf("Returning response with:\n")
	fmt.Printf("- %d clusters (including injected)\n", len(response.Clusters))
	fmt.Printf("- %d secrets\n", len(response.Secrets))
	fmt.Printf("- %d listeners (including injected)\n", len(response.Listeners))
	fmt.Printf("- %d routes (including injected)\n", len(response.Routes))

	return response, nil
}

func main() {
	fmt.Println("This demonstrates the new PostTranslateModifyHook functionality")
	fmt.Println("Extensions can now modify listeners and routes in addition to clusters and secrets")
	
	// Create example request
	req := &pb.PostTranslateModifyRequest{
		Clusters: []*clusterv3.Cluster{
			{Name: "test-cluster"},
		},
		Secrets: []*tlsv3.Secret{
			{Name: "test-secret"},
		},
		Listeners: []*listenerv3.Listener{
			{Name: "test-listener"},
		},
		Routes: []*routev3.RouteConfiguration{
			{Name: "test-route"},
		},
	}

	server := &ExampleExtensionServer{}
	resp, err := server.PostTranslateModify(context.Background(), req)
	if err != nil {
		log.Fatalf("Error: %v", err)
	}

	fmt.Printf("\nSuccess! Extension processed %d clusters, %d secrets, %d listeners, %d routes\n",
		len(resp.Clusters), len(resp.Secrets), len(resp.Listeners), len(resp.Routes))
}
